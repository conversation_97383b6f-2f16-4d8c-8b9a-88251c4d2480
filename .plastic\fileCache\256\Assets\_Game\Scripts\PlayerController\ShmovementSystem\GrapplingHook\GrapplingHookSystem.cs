using UnityEngine;
using System.Collections;
using KinematicCharacterController.FPS;

public class GrapplingHookSystem : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private Transform gunTip;
    [SerializeField] private Transform camera;
    [SerializeField] private Transform player;
    [SerializeField] private LayerMask grapplableMask;
    [SerializeField] private LineRenderer lineRenderer;
    [SerializeField] private Transform predictionPoint;
    [SerializeField] private FPSCharacterController playerMovement;
    [SerializeField] private PredictionVisualizer predictionVisualizer;

    [Header("Swinging")]
    [SerializeField] private float maxSwingDistance = 25f;
    [SerializeField] private float swingForce = 4f;
    [SerializeField] private float swingSpeed = 10f;

    [Header("Grapple Physics")]
    [SerializeField] private float swingGravity = 5f;         // Reduced gravity while swinging
    [SerializeField] private float ropeSpringForce = 50f;     // How strongly the rope pulls you
    [SerializeField] private float maxRopeTension = 100f;     // Maximum force the rope can exert
    [SerializeField] private float ropeDamping = 5f;          // How quickly oscillations dampen
    [SerializeField] private float maxDistanceMultiplier = 0.8f;
    [SerializeField] private float minDistanceMultiplier = 0.25f;
    [SerializeField] private float ropeSlack = 0.5f;          // Extra length before the rope becomes taut
    [SerializeField, Range(0f, 1f)] private float gravityScaleWhileSwinging = 1f; // 1 = normal gravity, <1 for lighter gravity while swinging

    [Header("Air Control")]
    [SerializeField] private float horizontalThrustForce = 4f;
    [SerializeField] private float forwardThrustForce = 2f;
    [SerializeField] private float extendCableSpeed = 10f;
    [SerializeField] private float shortenCableSpeed = 10f;

    [Header("Prediction")]
    [SerializeField] private float predictionSphereCastRadius = 2f;
    [SerializeField] private Material predictionMaterial;

    [Header("Sphere Visualization")]
    [SerializeField] private float baseSphereSize = 0.3f;
    [SerializeField] private Color sphereColor = Color.red;
    [SerializeField] private float distanceScaleFactor = 0.05f;
    [SerializeField] private float positionSmoothTime = 0.1f; // Controls smoothing speed (lower = faster)

    private Vector3 swingPoint;
    private float currentGrappleDistance;
    private Vector3 currentGrapplePosition;
    private Vector3 lastFramePosition;

    private RaycastHit predictionHit;
    private bool predictionFound;

    private bool isSwinging = false;
    public bool IsSwinging { get { return isSwinging; } }

    // New field to control prediction visibility
    private bool isPredictionEnabled = false;

    // Store original max speed for restoration later
    private float originalMaxSpeed;
    private float originalGravity;

    // References for the sphere visualization
    private MeshRenderer sphereRenderer;
    private Material sphereMaterial;

    // Variables for smooth movement
    private Vector3 targetSpherePosition;
    private Vector3 currentSphereVelocity = Vector3.zero;

    private void Awake()
    {
        if (playerMovement == null)
            playerMovement = GetComponent<FPSCharacterController>();

        if (camera == null && Camera.main != null)
            camera = Camera.main.transform;

        if (player == null)
            player = transform;

        if (lineRenderer == null)
        {
            lineRenderer = gameObject.AddComponent<LineRenderer>();
            SetupLineRenderer();
        }

        if (predictionVisualizer == null)
            predictionVisualizer = GetComponent<PredictionVisualizer>();

        // Setup prediction sphere if not assigned
        if (predictionPoint == null)
        {
            CreatePredictionSphere();
        }

        lastFramePosition = transform.position;
    }

    private void CreatePredictionSphere()
    {
        // Create empty parent object for the sphere
        GameObject sphereObj = new GameObject("GrapplePredictionSphere");
        predictionPoint = sphereObj.transform;

        // Add a sphere mesh to the object
        GameObject sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        sphere.transform.SetParent(predictionPoint);
        sphere.transform.localPosition = Vector3.zero;
        sphere.transform.localScale = Vector3.one * baseSphereSize;

        // Set up the material
        sphereRenderer = sphere.GetComponent<MeshRenderer>();

        // If we have a material assigned in the inspector, use it, otherwise create a new one
        if (predictionMaterial != null)
        {
            sphereMaterial = new Material(predictionMaterial);
        }
        else
        {
            // Create a new material with emission for the glow effect
            sphereMaterial = new Material(Shader.Find("Standard"));
            sphereMaterial.EnableKeyword("_EMISSION");
            sphereMaterial.SetColor("_EmissionColor", sphereColor * 2.0f);
        }

        sphereMaterial.color = sphereColor;
        sphereRenderer.material = sphereMaterial;

        // Remove collider as we don't need physics on the visualization
        Destroy(sphere.GetComponent<Collider>());

        // Configure the sphere to start hidden
        sphereObj.SetActive(false);

        // Initialize target position
        targetSpherePosition = predictionPoint.position;
    }

    private void UpdateSphereVisualization(RaycastHit hit)
    {
        if (predictionPoint == null) return;

        // Set the target position with slight offset along normal
        targetSpherePosition = hit.point + hit.normal * 0.05f;

        // Smoothly move the sphere to the target position
        predictionPoint.position = Vector3.SmoothDamp(
            predictionPoint.position,
            targetSpherePosition,
            ref currentSphereVelocity,
            positionSmoothTime
        );

        // Scale based on distance (further away = larger for visibility)
        float distance = Vector3.Distance(camera.position, hit.point);
        float adaptiveSize = baseSphereSize + (distance * distanceScaleFactor);
        adaptiveSize = Mathf.Clamp(adaptiveSize, baseSphereSize * 0.5f, baseSphereSize * 3f);

        // Apply scale to the sphere
        predictionPoint.localScale = Vector3.one * adaptiveSize;
    }

    private void SetupLineRenderer()
    {
        lineRenderer.startWidth = 0.1f;
        lineRenderer.endWidth = 0.1f;
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
        lineRenderer.startColor = Color.black;
        lineRenderer.endColor = Color.black;
        lineRenderer.positionCount = 0;
    }

    private void Update()
    {
        // Only check for swing points if prediction is enabled
        if (isPredictionEnabled)
        {
            // If we have a prediction visualizer component, use it instead
            if (predictionVisualizer != null)
            {
                predictionFound = predictionVisualizer.HasPredictionPoint();
                if (predictionFound)
                    predictionHit = predictionVisualizer.GetPredictionHit();
            }
            else
            {
                CheckForSwingPoint();
            }
        }
        else
        {
            // Hide prediction point when not enabled
            if (predictionPoint != null)
            {
                predictionPoint.gameObject.SetActive(false);
            }
            predictionFound = false;
        }

        if (isSwinging)
        {
            HandleSwinging();
            HandleSwingAirControl();
        }

        lastFramePosition = transform.position;
    }

    private void LateUpdate()
    {
        DrawRope();
    }

    public void FireGrapple()
    {
        if (isSwinging) return;

        bool canGrapple = false;
        Vector3 grappleTarget = Vector3.zero;

        // Use either prediction visualizer or our own prediction
        if (predictionVisualizer != null && predictionVisualizer.HasPredictionPoint())
        {
            canGrapple = true;
            grappleTarget = predictionVisualizer.GetPredictionPoint();
        }
        else if (predictionFound)
        {
            canGrapple = true;
            grappleTarget = predictionHit.point;
        }

        if (canGrapple)
        {
            isSwinging = true;
            swingPoint = grappleTarget;
            currentGrappleDistance = Vector3.Distance(player.position, swingPoint) + ropeSlack;
            StartSwing();
        }
    }

    public void ReleaseGrapple()
    {
        if (isSwinging)
        {
            StopSwing();
        }
    }

    private void CheckForSwingPoint()
    {
        // Don't check for swing points if prediction is disabled or already swinging
        if (isSwinging || !isPredictionEnabled) return;

        predictionFound = false;

        // Try precise raycast first
        RaycastHit raycastHit;
        bool rayHit = Physics.Raycast(camera.position, camera.forward, out raycastHit, maxSwingDistance, grapplableMask);

        // Try spherecast for broader detection
        RaycastHit spherecastHit;
        bool sphereHit = Physics.SphereCast(camera.position, predictionSphereCastRadius, camera.forward,
            out spherecastHit, maxSwingDistance, grapplableMask);

        // Choose which hit to use - prefer raycast if available
        if (rayHit)
        {
            predictionHit = raycastHit;
            predictionFound = true;
        }
        else if (sphereHit)
        {
            predictionHit = spherecastHit;
            predictionFound = true;
        }

        // Update prediction sphere visualization
        if (predictionPoint != null)
        {
            if (predictionFound)
            {
                // Make sure the object is active before updating
                if (!predictionPoint.gameObject.activeSelf)
                {
                    predictionPoint.gameObject.SetActive(true);
                    // Initialize position to avoid jarring jump when first activated
                    predictionPoint.position = predictionHit.point + predictionHit.normal * 0.05f;
                    targetSpherePosition = predictionPoint.position;
                    currentSphereVelocity = Vector3.zero;
                }
                UpdateSphereVisualization(predictionHit);
            }
            else
            {
                predictionPoint.gameObject.SetActive(false);
            }
        }
    }

    private void StartSwing()
    {
        // Store original values to restore later
        originalMaxSpeed = playerMovement.MaxStableMoveSpeed;
        originalGravity = playerMovement.Gravity.y;

        // Set swinging state in character controller
        SetPlayerSwingingState(true);

        // Setup line renderer
        lineRenderer.positionCount = 2;
        currentGrapplePosition = gunTip.position;

        // Set the maximum rope length
        currentGrappleDistance = Vector3.Distance(player.position, swingPoint) + ropeSlack;

        // Ensure the hook prediction is disabled while swinging
        if (predictionPoint != null)
            predictionPoint.gameObject.SetActive(false);
    }

    private void StopSwing()
    {
        isSwinging = false;

        // Restore character controller settings
        SetPlayerSwingingState(false);

        // Reset line renderer
        lineRenderer.positionCount = 0;
    }

    private void HandleSwinging()
    {
        // Only apply rope physics if player is airborne
        if (!playerMovement.Motor.GroundingStatus.IsStableOnGround)
        {
            // Calculate current distance to grapple point
            float distanceToGrapplePoint = Vector3.Distance(player.position, swingPoint);

            // Calculate direction to grapple point
            Vector3 directionToGrapplePoint = (swingPoint - player.position).normalized;

            // Calculate how much the rope is being stretched
            float ropeStretchAmount = Mathf.Max(0, distanceToGrapplePoint - currentGrappleDistance);

            // Calculate spring force based on stretch
            float tensionForce = ropeStretchAmount * ropeSpringForce;

            // Calculate player velocity in the rope direction
            Vector3 playerVelocity = (player.position - lastFramePosition) / Time.deltaTime;
            float velocityInRopeDirection = Vector3.Dot(playerVelocity, directionToGrapplePoint);

            // Apply damping to spring force (stronger damping when moving toward the hook)
            if (velocityInRopeDirection < 0)
            {
                tensionForce += -velocityInRopeDirection * ropeDamping;
            }

            // Clamp the maximum force of the rope
            tensionForce = Mathf.Min(tensionForce, maxRopeTension);

            // Apply force to player if rope is stretched
            if (ropeStretchAmount > 0)
            {
                Vector3 forceToApply = directionToGrapplePoint * tensionForce * Time.deltaTime;
                playerMovement.AddVelocity(forceToApply);
            }
        }
        else
        {
            // Player is on ground - just maintain the grapple visually but don't apply forces
            // This prevents the rope from interfering with ground movement
        }
    }


    private void HandleSwingAirControl()
    {
        if (!isSwinging) return;

        // Only allow air control when actually airborne
        if (!playerMovement.Motor.GroundingStatus.IsStableOnGround)
        {
            // Horizontal thrust (left/right)
            if (Input.GetKey(KeyCode.A))
                AddAirControlForce(-camera.right);

            if (Input.GetKey(KeyCode.D))
                AddAirControlForce(camera.right);

            // Forward thrust
            if (Input.GetKey(KeyCode.W))
                AddAirControlForce(camera.forward);

            // Rope controls
            if (Input.GetKey(KeyCode.Space)) // Shorten rope and pull
            {
                // Pull towards swing point
                Vector3 directionToPoint = (swingPoint - transform.position).normalized;
                playerMovement.AddVelocity(directionToPoint * swingForce * Time.deltaTime);

                // Shorten rope
                currentGrappleDistance -= shortenCableSpeed * Time.deltaTime;
            }

            if (Input.GetKey(KeyCode.S)) // Extend rope
            {
                currentGrappleDistance += extendCableSpeed * Time.deltaTime;
            }
        }
    }


    private void AddAirControlForce(Vector3 direction)
    {
        // Calculate and add force in the specified direction
        float forceMultiplier = (direction == camera.forward) ? forwardThrustForce : horizontalThrustForce;
        playerMovement.AddVelocity(direction * forceMultiplier * Time.deltaTime);
    }

    private void DrawRope()
    {
        if (!isSwinging || lineRenderer.positionCount != 2) return;

        // Update grapple position with lerp for smooth rope appearance
        currentGrapplePosition = Vector3.Lerp(currentGrapplePosition, swingPoint, Time.deltaTime * swingSpeed);

        // Set line renderer positions
        lineRenderer.SetPosition(0, gunTip.position);
        lineRenderer.SetPosition(1, currentGrapplePosition);
    }

    private void SetPlayerSwingingState(bool isSwinging)
    {
        if (isSwinging)
        {
            // Only apply speed boost if player is airborne
            if (!playerMovement.Motor.GroundingStatus.IsStableOnGround)
            {
                playerMovement.MaxStableMoveSpeed = 30f; // Use higher value for swinging
            }

            // Optionally scale gravity while grappling instead of hard-coding a reduced value
            playerMovement.Gravity = new Vector3(0, originalGravity * gravityScaleWhileSwinging, 0);
        }
        else
        {
            // Restore original values
            playerMovement.MaxStableMoveSpeed = originalMaxSpeed;
            playerMovement.Gravity = new Vector3(0, originalGravity, 0);
        }
    }


    private void OnDisable()
    {
        StopSwing();
    }

    // Public method to enable/disable prediction visualization
    public void SetPredictionEnabled(bool enabled)
    {
        isPredictionEnabled = enabled;

        // Hide prediction immediately when disabled
        if (!enabled && predictionPoint != null)
        {
            predictionPoint.gameObject.SetActive(false);
        }
    }
}